"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[69],{17069:(t,e,s)=>{s.d(e,{rM:()=>m,fx:()=>y});var i=s(95155),a=s(12115),r=s(81115),o=s(98915),n=s(50475),c=s(91317),l=s(27759);class u{subscribe(t){return this.subscribers.push(t),t(this.state),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(){this.subscribers.forEach(t=>t(this.state))}async initializeForUser(t){this.userId=t,this.state.isLoading=!0,this.notifySubscribers();try{await this.initializeAccountInfo(),this.setupRealtimeListeners(),this.state.isLoading=!1,this.state.error=null,this.notifySubscribers(),console.log("Realtime trading service initialized for user:",t)}catch(t){console.error("Error initializing realtime trading service:",t),this.state.isLoading=!1,this.state.error="Failed to initialize trading service",this.notifySubscribers()}}async initializeAccountInfo(){if(!this.userId)return;let t=n.A.getUserBalance();this.state.accountInfo={totalWalletBalance:t,totalUnrealizedProfit:0,totalMarginBalance:t,totalPositionInitialMargin:0,totalOpenOrderInitialMargin:0,availableBalance:t,maxWithdrawAmount:t,updateTime:Date.now()}}setupRealtimeListeners(){if(!this.userId)return;let t=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/positions")),e=(0,r.Zy)(t,t=>{let e=t.val();this.state.positions=e?Object.entries(e).map(t=>{let[e,s]=t;return{id:e,...s,timestamp:s.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}),s=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/orders")),i=(0,r.Zy)(s,t=>{let e=t.val();this.state.orders=e?Object.entries(e).map(t=>{let[e,s]=t;return{id:e,...s,timestamp:s.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}),a=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/trades")),n=(0,r.Zy)(a,t=>{let e=t.val();this.state.trades=e?Object.entries(e).map(t=>{let[e,s]=t;return{id:e,...s,timestamp:s.timestamp||Date.now()}}).sort((t,e)=>e.timestamp-t.timestamp):[],this.notifySubscribers()});this.unsubscribeFunctions=[()=>(0,r.AU)(t,"value",e),()=>(0,r.AU)(s,"value",i),()=>(0,r.AU)(a,"value",n)]}updateAccountInfo(){if(!this.state.accountInfo)return;let t=n.A.getUserBalance(),e=this.state.positions.reduce((t,e)=>t+(e.pnl||0),0),s=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),i=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>t+e.quantity*e.price/(e.leverage||10),0);this.state.accountInfo.totalWalletBalance=t,this.state.accountInfo.totalUnrealizedProfit=e,this.state.accountInfo.totalPositionInitialMargin=s,this.state.accountInfo.totalOpenOrderInitialMargin=i,this.state.accountInfo.totalMarginBalance=t+e,this.state.accountInfo.availableBalance=Math.max(0,t-s-i),this.state.accountInfo.maxWithdrawAmount=Math.max(0,t-s-i),this.state.accountInfo.updateTime=Date.now()}updateMarketData(t,e){this.state.marketData[t]={...this.state.marketData[t],...e},e.price&&(this.state.positions=this.state.positions.map(s=>s.symbol===t?this.updatePositionPnL(s,e.price):s),this.userId&&this.state.positions.forEach(e=>{if(e.symbol===t){let t=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/positions/").concat(e.id));(0,r.hZ)(t,{...e,updatedAt:(0,r.O5)()}).catch(t=>console.error("Error updating position PnL:",t))}}),this.updateAccountInfo(),this.notifySubscribers())}calculatePnL(t,e){let s=("LONG"===t.side?e-t.entryPrice:t.entryPrice-e)*t.size,i=t.margin>0?s/t.margin*100:0;return{pnl:s,pnlPercent:i}}updatePositionPnL(t,e){let{pnl:s,pnlPercent:i}=this.calculatePnL(t,e);return{...t,markPrice:e,pnl:s,pnlPercent:i}}calculateLiquidationPrice(t,e,s){let i=.995-1/s;return"LONG"===e?t*i:t*(2-i)}async placeOrder(t){var e,s;if(!this.userId)throw Error("User not authenticated");let i=(null===(e=this.state.marketData[t.symbol])||void 0===e?void 0:e.price)||t.price||0,a=t.quantity*i/(t.leverage||10);if(a>((null===(s=this.state.accountInfo)||void 0===s?void 0:s.availableBalance)||0))throw Error("Insufficient available balance. Required: ".concat(a.toFixed(2)," USDT"));let n="ord_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),c={id:n,symbol:t.symbol,side:t.side,type:t.type,quantity:t.quantity,price:t.price||i,status:"NEW",timestamp:Date.now(),executedQty:0,leverage:t.leverage||10};this.state.orders.push(c),this.updateAccountInfo(),this.notifySubscribers();try{let e=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/orders/").concat(n));return await (0,r.hZ)(e,{...c,createdAt:(0,r.O5)()}),"MARKET"===t.type&&await this.executeOrder(n,c,i),l.l.createTradeNotification(this.userId,"order_placed",{symbol:t.symbol,side:t.side,type:t.type,price:t.price||i,quantity:t.quantity}),n}catch(t){throw this.state.orders=this.state.orders.filter(t=>t.id!==n),this.updateAccountInfo(),this.notifySubscribers(),t}}async executeOrder(t,e,s){if(!this.userId)return;let i=e.quantity*s*.001,a=e.quantity*s/e.leverage,u="pos_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),d={id:u,symbol:e.symbol,side:"BUY"===e.side?"LONG":"SHORT",entryPrice:s,markPrice:s,size:e.quantity,margin:a,leverage:e.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(s,"BUY"===e.side?"LONG":"SHORT",e.leverage),timestamp:Date.now(),orderId:t};this.state.positions.push(d);let h=this.state.orders.findIndex(e=>e.id===t);-1!==h&&(this.state.orders[h].status="FILLED",this.state.orders[h].executedQty=e.quantity),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_opened",{symbol:e.symbol,side:d.side,size:e.quantity,entryPrice:s});try{let e=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/positions/").concat(u)),s=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/orders/").concat(t));await Promise.all([(0,r.hZ)(e,{...d,createdAt:(0,r.O5)()}),(0,r.hZ)(s,{...this.state.orders[h],updatedAt:(0,r.O5)()})]);let a=n.A.getUserBalance();await n.A.updateBalance(a-i,"commission","Trading commission: ".concat(i.toFixed(2)," USDT")),c.b.addPosition(this.userId,d).catch(console.error)}catch(t){console.error("Error saving position to Firebase:",t)}}async closePosition(t){var e;if(!this.userId)return;let s=this.state.positions.findIndex(e=>e.id===t);if(-1===s)return;let i=this.state.positions[s],a=(null===(e=this.state.marketData[i.symbol])||void 0===e?void 0:e.price)||i.markPrice,u=i.size*a*.001;this.state.positions.splice(s,1),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_closed",{symbol:i.symbol,side:i.side,pnl:i.pnl,closePrice:a});try{let e="trade_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),s={id:e,symbol:i.symbol,side:"LONG"===i.side?"SELL":"BUY",price:a,quantity:i.size,commission:u,realizedPnl:i.pnl,timestamp:Date.now(),leverage:i.leverage,orderId:i.orderId||"",positionId:t};this.state.trades.unshift(s),this.notifySubscribers();let l=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/positions/").concat(t)),d=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/trades/").concat(e));await Promise.all([(0,r.TF)(l),(0,r.hZ)(d,{...s,createdAt:(0,r.O5)()})]);let h=n.A.getUserBalance();await n.A.updateBalance(h+i.pnl-u,i.pnl>0?"trade_profit":"trade_loss","Position closed: ".concat(i.pnl>0?"+":"").concat(i.pnl.toFixed(2)," USDT")),c.b.addTrade(this.userId,s).catch(console.error)}catch(t){throw console.error("Error closing position in Firebase:",t),this.state.positions.push(i),this.updateAccountInfo(),this.notifySubscribers(),t}}getState(){return{...this.state}}getMarketData(t){return this.state.marketData[t]||null}cleanup(){this.unsubscribeFunctions.forEach(t=>t()),this.unsubscribeFunctions=[],this.userId=null,this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.notifySubscribers()}constructor(){this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.subscribers=[],this.userId=null,this.unsubscribeFunctions=[],n.A.subscribe(t=>{t?this.initializeForUser(t.id):this.cleanup()})}}let d=new u;class h{initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(t=>{this.simulators.set(t.symbol,{symbol:t.symbol,basePrice:t.basePrice,volatility:t.volatility,trend:(Math.random()-.5)*.001,lastPrice:t.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},1e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(t=>{let e=Date.now(),s=(e-t.lastUpdate)/1e3,i=(Math.random()-.5)*t.volatility*s,a=t.trend*s,r=t.lastPrice*(1+(i+a)),o=r-t.lastPrice,n=o/t.lastPrice*100;.01>Math.random()&&(t.trend=(Math.random()-.5)*.001),t.lastPrice=r,t.lastUpdate=e;let c={symbol:t.symbol,price:r,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:e};this.notifySubscribers(t.symbol,c)})}subscribe(t){return this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(t,e){this.subscribers.forEach(s=>s(t,e))}getCurrentPrice(t){let e=this.simulators.get(t);return e?e.lastPrice:null}addSymbol(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.03;this.simulators.has(t)||this.simulators.set(t,{symbol:t,basePrice:e,volatility:s,trend:(Math.random()-.5)*.001,lastPrice:e,lastUpdate:Date.now()})}removeSymbol(t){this.simulators.delete(t)}constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}}let b=new h,p=(0,a.createContext)(void 0);function m(t){let{children:e}=t,[s,r]=(0,a.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null});(0,a.useEffect)(()=>d.subscribe(t=>{r(t)}),[]),(0,a.useEffect)(()=>{let t=b.subscribe((t,e)=>{d.updateMarketData(t,e)});return b.start(),()=>{t(),b.stop()}},[]);let o=(0,a.useCallback)(async t=>{try{return await d.placeOrder(t)}catch(t){throw console.error("Failed to place order:",t),t}},[]),n=(0,a.useCallback)(async t=>{try{return console.log("Cancel order not implemented yet:",t),!0}catch(t){throw console.error("Failed to cancel order:",t),t}},[]),c=(0,a.useCallback)(async t=>{try{return await d.closePosition(t),!0}catch(t){throw console.error("Failed to close position:",t),t}},[]),l=(0,a.useCallback)(async t=>{try{return console.log("Position update not implemented in Firebase service yet:",t),!0}catch(t){throw console.error("Failed to update position:",t),t}},[]),u=(0,a.useCallback)((t,e)=>{d.updateMarketData(t,e)},[]),h=(0,a.useCallback)(t=>{console.log("Account info update not needed with Realtime service:",t)},[]),m=(0,a.useCallback)(()=>{r(t=>({...t,error:null}))},[]),y=(0,a.useCallback)(t=>d.getMarketData(t),[]),f=(0,a.useCallback)(t=>s.positions.find(e=>e.symbol===t)||null,[s.positions]),I=(0,a.useCallback)(()=>s.accountInfo,[s.accountInfo]),v=(0,a.useCallback)(()=>s.positions.reduce((t,e)=>t+e.pnl,0),[s.positions]),P=(0,a.useCallback)(()=>s.positions.reduce((t,e)=>t+e.margin,0),[s.positions]),g=(0,a.useCallback)(()=>{var t;return(null===(t=s.accountInfo)||void 0===t?void 0:t.availableBalance)||0},[s.accountInfo]),D={positions:s.positions,orders:s.orders,trades:s.trades,marketData:s.marketData,accountInfo:s.accountInfo,isLoading:s.isLoading,error:s.error,state:s,placeOrder:o,cancelOrder:n,closePosition:c,updatePosition:l,updateMarketData:u,updateAccountInfo:h,clearError:m,getMarketData:y,getPositionBySymbol:f,getAccountInfo:I,getTotalPnL:v,getTotalMargin:P,getAvailableBalance:g};return(0,i.jsx)(p.Provider,{value:D,children:e})}function y(){let t=(0,a.useContext)(p);if(void 0===t)throw Error("useTrading must be used within a TradingProvider");return t}}}]);